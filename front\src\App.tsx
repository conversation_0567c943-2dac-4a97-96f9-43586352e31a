import { Routes, Route } from 'react-router-dom'
import { AuthProvider } from './store/authStore'
import Layout from './components/Layout'
import HomePage from './pages/HomePage'
import LoginPage from './pages/LoginPage'
import SignupPage from './pages/SignupPage'
import GamePage from './pages/GamePage'

function App() {
  return (
    <AuthProvider>
      <div className="min-h-screen bg-background nuclear-theme">
        <Routes>
          <Route path="/" element={<Layout />}>
            <Route index element={<HomePage />} />
            <Route path="login" element={<LoginPage />} />
            <Route path="signup" element={<SignupPage />} />
            <Route path="game" element={<GamePage />} />
          </Route>
        </Routes>
      </div>
    </AuthProvider>
  )
}

export default App
