services:
  # Databases
  postgres-auth:
    image: postgres:15-alpine
    container_name: nuclearstory-postgres-auth-dev
    environment:
      POSTGRES_DB: auth_db
      POSTGRES_USER: nuclearstory
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_auth_data_dev:/var/lib/postgresql/data
      - ./postgres/init-auth.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - nuclearstory-network

  postgres-saves:
    image: postgres:15-alpine
    container_name: nuclearstory-postgres-saves-dev
    environment:
      POSTGRES_DB: saves_db
      POSTGRES_USER: nuclearstory
      POSTGRES_PASSWORD: password
    ports:
      - "5433:5432"
    volumes:
      - postgres_saves_data_dev:/var/lib/postgresql/data
      - ./postgres/init-saves.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - nuclearstory-network

  redis:
    image: redis:7-alpine
    container_name: nuclearstory-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data_dev:/data
    networks:
      - nuclearstory-network

  # AI Service
  ai-service:
    build:
      context: ../ai/ai-service
      dockerfile: Dockerfile
    container_name: nuclearstory-ai-service-dev
    ports:
      - "3005:3005"
    environment:
      - NODE_ENV=development
      - PORT=3005
      - CORS_ORIGIN=http://localhost:3000,http://localhost:80
      - LOG_LEVEL=INFO
    volumes:
      - ../ai/ai-service:/app
      - /app/__pycache__
    networks:
      - nuclearstory-network
    restart: unless-stopped

  # Backend Services with hot reload
  auth-service:
    build:
      context: ../back/auth-service
      dockerfile: Dockerfile.dev
    container_name: nuclearstory-auth-service-dev
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - DATABASE_HOST=postgres-auth
      - DATABASE_PORT=5432
      - DATABASE_USERNAME=nuclearstory
      - DATABASE_PASSWORD=password
      - DATABASE_NAME=auth_db
      - JWT_SECRET=dev-super-secret-jwt-key
      - JWT_EXPIRES_IN=7d
      - CORS_ORIGIN=http://localhost:3000
    volumes:
      - ../back/auth-service:/app
      - /app/node_modules
    depends_on:
      - postgres-auth
    networks:
      - nuclearstory-network
    restart: unless-stopped

  game-engine-service:
    build:
      context: ../back/game-engine-service
      dockerfile: Dockerfile.dev
    container_name: nuclearstory-game-engine-service-dev
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=development
      - PORT=3002
      - AUTH_SERVICE_URL=http://auth-service:3001
      - STORY_SERVICE_URL=http://story-service:3003
      - SAVE_SERVICE_URL=http://save-service:3004
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - CORS_ORIGIN=http://localhost:3000
    volumes:
      - ../back/game-engine-service:/app
      - /app/node_modules
    depends_on:
      - auth-service
      - redis
    networks:
      - nuclearstory-network
    restart: unless-stopped

  story-service:
    build:
      context: ../back/story-service
      dockerfile: Dockerfile.dev
    container_name: nuclearstory-story-service-dev
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=development
      - PORT=3003
      - AUTH_SERVICE_URL=http://auth-service:3001
      - AI_SERVICE_URL=http://ai-service:3005
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - CORS_ORIGIN=http://localhost:3000
    volumes:
      - ../back/story-service:/app
      - /app/node_modules
    depends_on:
      - auth-service
      - ai-service
      - redis
    networks:
      - nuclearstory-network
    restart: unless-stopped

  save-service:
    build:
      context: ../back/save-service
      dockerfile: Dockerfile.dev
    container_name: nuclearstory-save-service-dev
    ports:
      - "3004:3004"
    environment:
      - NODE_ENV=development
      - PORT=3004
      - DATABASE_HOST=postgres-saves
      - DATABASE_PORT=5432
      - DATABASE_USERNAME=nuclearstory
      - DATABASE_PASSWORD=password
      - DATABASE_NAME=saves_db
      - AUTH_SERVICE_URL=http://auth-service:3001
      - CORS_ORIGIN=http://localhost:3000
    volumes:
      - ../back/save-service:/app
      - /app/node_modules
    depends_on:
      - postgres-saves
      - auth-service
    networks:
      - nuclearstory-network
    restart: unless-stopped

  # Frontend with hot reload
  frontend:
    build:
      context: ../front
      dockerfile: Dockerfile.dev
    container_name: nuclearstory-frontend-dev
    ports:
      - "3000:3000"
    environment:
      - VITE_API_URL=http://localhost:80/api
      - VITE_AUTH_SERVICE_URL=http://localhost:80/api/auth
      - VITE_GAME_SERVICE_URL=http://localhost:80/api/game
      - VITE_STORY_SERVICE_URL=http://localhost:80/api/story
      - VITE_SAVE_SERVICE_URL=http://localhost:80/api/save
      - VITE_AI_SERVICE_URL=http://localhost:80/api/ai
    volumes:
      - ../front:/app
      - /app/node_modules
    networks:
      - nuclearstory-network

  # Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: nuclearstory-nginx-dev
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - auth-service
      - game-engine-service
      - story-service
      - save-service
      - ai-service
      - frontend
    networks:
      - nuclearstory-network
    restart: unless-stopped

volumes:
  postgres_auth_data_dev:
  postgres_saves_data_dev:
  redis_data_dev:

networks:
  nuclearstory-network:
    driver: bridge
