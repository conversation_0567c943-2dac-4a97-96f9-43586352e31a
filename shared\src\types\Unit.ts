import { NPCType, FactionAlignment } from '../enums'
import { Position, Special, Effect } from '../models/Player'

export interface UnitStats {
  // Primary attributes
  special: Special
  
  // Derived stats
  hitPoints: number
  actionPoints: number
  carryWeight: number
  meleeDamage: number
  damageResistance: number
  poisonResistance: number
  radiationResistance: number
  
  // Combat stats
  sequence: number // Initiative
  healingRate: number
  criticalChance: number
  
  // Skills
  skills: Record<string, number>
}

export interface UnitEquipment {
  weapon?: string // Item ID
  armor?: string // Item ID
  helmet?: string // Item ID
  gloves?: string // Item ID
  boots?: string // Item ID
  accessory?: string // Item ID
}

export interface UnitAI {
  type: 'aggressive' | 'defensive' | 'support' | 'flee' | 'patrol' | 'guard'
  aggroRange: number
  fleeThreshold: number // HP percentage
  preferredRange: 'melee' | 'ranged' | 'any'
  targetPriority: 'nearest' | 'weakest' | 'strongest' | 'player'
  useItems: boolean
  callForHelp: boolean
  helpRadius: number
}

export interface UnitBehavior {
  isHostile: boolean
  isNeutral: boolean
  canTalk: boolean
  canTrade: boolean
  flees: boolean 
  callsForHelp: boolean
  
  // Faction behavior
  factionId?: string
  factionRank?: string
  alignment: FactionAlignment
  
  // Reputation with player
  reputation: number
  
  // Dialogue
  dialogueId?: string
  greetingText?: string
  combatTaunts?: string[]
}

export interface Unit {
  id: string
  name: string
  description: string
  type: NPCType
  
  // Basic properties
  level: number
  experience: number
  
  // Current state
  currentHP: number
  maxHP: number
  currentAP: number
  maxAP: number
  
  // Position and movement
  position: Position
  facing: 'north' | 'south' | 'east' | 'west'
  movementRange: number
  hasActed: boolean
  hasMoved: boolean
  
  // Stats and abilities
  stats: UnitStats
  
  // Equipment and inventory
  equipment: UnitEquipment
  inventory: string[] // Item IDs
  carryCapacity: number
  currentWeight: number
  
  // Status and effects
  isAlive: boolean
  isConscious: boolean
  isStunned: boolean
  isParalyzed: boolean
  isBlinded: boolean
  effects: Effect[]
  
  // Combat state
  isInCombat: boolean
  lastAttacker?: string // Unit ID
  combatTarget?: string // Unit ID
  
  // AI and behavior
  ai?: UnitAI
  behavior: UnitBehavior
  
  // Appearance
  sprite?: string
  avatar?: string
  size: 'small' | 'medium' | 'large' | 'huge'
  
  // Loot and rewards
  lootTable?: string
  experienceReward: number
  currencyReward: number
  
  // Spawn and respawn
  spawnLocationId?: string
  canRespawn: boolean
  respawnTime?: number // minutes
  respawnAt?: Date
  
  // Special flags
  isEssential: boolean // Cannot be killed
  isUnique: boolean // Only one instance
  isBoss: boolean
  isMinion: boolean
  masterId?: string // Unit ID of master (for minions)
  
  // Timestamps
  createdAt: Date
  lastActionAt?: Date
  deathAt?: Date
}
