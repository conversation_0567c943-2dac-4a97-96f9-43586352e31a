#!/bin/bash

echo "🔍 Checking Docker setup..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed"
    exit 1
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker Desktop."
    exit 1
fi

# Check Docker Compose
if docker compose version > /dev/null 2>&1; then
    echo "✅ Docker Compose v2 available"
    COMPOSE_CMD="docker compose"
elif docker-compose version > /dev/null 2>&1; then
    echo "✅ Docker Compose v1 available"
    COMPOSE_CMD="docker-compose"
else
    echo "❌ Docker Compose not found"
    exit 1
fi

echo "✅ Docker is ready!"
echo "📋 Docker info:"
docker version --format "   Docker: {{.Server.Version}}"
$COMPOSE_CMD version --short 2>/dev/null | head -1 | sed 's/^/   Compose: /'

echo ""
echo "🚀 You can now run: ./scripts/quick-start.sh"
