# NuclearStory - Development Start Script with Hot Reload (PowerShell)
Write-Host "🛠️  Starting NuclearStory in DEVELOPMENT mode with hot reload..." -ForegroundColor Green

# Check if Docker is running
try {
    docker info | Out-Null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker not running"
    }
} catch {
    Write-Host "❌ Docker is not running. Please start Docker Desktop first." -ForegroundColor Red
    exit 1
}

# Check if .env file exists
if (-not (Test-Path ".env")) {
    Write-Host "⚠️  .env file not found. Creating default .env file..." -ForegroundColor Yellow
    @"
# NuclearStory Development Environment
NODE_ENV=development
POSTGRES_PASSWORD=password
POSTGRES_USER=nuclearstory
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
CORS_ORIGIN=http://localhost:3000
LOG_LEVEL=INFO
"@ | Out-File -FilePath ".env" -Encoding UTF8
    Write-Host "📝 .env file created with default values for development." -ForegroundColor Green
}

Write-Host "🔧 Building development images..." -ForegroundColor Cyan

# Build all development images
docker compose -f docker-compose.dev.yml build

Write-Host "🗄️  Starting databases and Redis..." -ForegroundColor Cyan
docker compose -f docker-compose.dev.yml up -d postgres-auth postgres-saves redis

# Wait for databases to be ready
Write-Host "⏳ Waiting for databases to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 15

Write-Host "🤖 Starting AI service..." -ForegroundColor Cyan
docker compose -f docker-compose.dev.yml up -d ai-service

# Wait for AI service
Write-Host "⏳ Waiting for AI service..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

Write-Host "🔧 Starting backend services with hot reload..." -ForegroundColor Cyan
docker compose -f docker-compose.dev.yml up -d auth-service game-engine-service story-service save-service

# Wait for backend services
Write-Host "⏳ Waiting for backend services..." -ForegroundColor Yellow
Start-Sleep -Seconds 20

Write-Host "🎨 Starting frontend with hot reload..." -ForegroundColor Cyan
docker compose -f docker-compose.dev.yml up -d frontend

Write-Host "🌐 Starting nginx proxy..." -ForegroundColor Cyan
docker compose -f docker-compose.dev.yml up -d nginx

Write-Host ""
Write-Host "✅ NuclearStory development environment is ready!" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 Access points:" -ForegroundColor White
Write-Host "   🎮 Game (Frontend): http://localhost:3000" -ForegroundColor Cyan
Write-Host "   🌍 API Gateway: http://localhost:80" -ForegroundColor Cyan
Write-Host "   🔐 Auth Service: http://localhost:3001/api/docs" -ForegroundColor Cyan
Write-Host "   🎯 Game Engine: http://localhost:3002/api/docs" -ForegroundColor Cyan
Write-Host "   📖 Story Service: http://localhost:3003/api/docs" -ForegroundColor Cyan
Write-Host "   💾 Save Service: http://localhost:3004/api/docs" -ForegroundColor Cyan
Write-Host "   🤖 AI Service: http://localhost:3005/docs" -ForegroundColor Cyan
Write-Host ""
Write-Host "🔥 HOT RELOAD ENABLED:" -ForegroundColor Yellow
Write-Host "   - Edit files in /front, /back, /ai folders" -ForegroundColor White
Write-Host "   - Changes will be automatically reflected" -ForegroundColor White
Write-Host "   - No need to rebuild containers" -ForegroundColor White
Write-Host ""
Write-Host "📊 Monitor with: .\scripts\dev-logs.ps1" -ForegroundColor Magenta
Write-Host "📈 Check status: .\scripts\status.ps1" -ForegroundColor Magenta
Write-Host "🛑 Stop with: .\scripts\dev-stop.ps1" -ForegroundColor Magenta
