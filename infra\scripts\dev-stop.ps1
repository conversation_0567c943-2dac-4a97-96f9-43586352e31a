# NuclearStory - Development Stop Script (PowerShell)
Write-Host "🛑 Stopping NuclearStory development environment..." -ForegroundColor Red

# Check if Docker is running
try {
    docker info | Out-Null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker not running"
    }
} catch {
    Write-Host "❌ Docker is not running. Nothing to stop." -ForegroundColor Yellow
    exit 0
}

Write-Host "🔄 Stopping all services..." -ForegroundColor Yellow
docker compose -f docker-compose.dev.yml down

Write-Host ""
Write-Host "✅ NuclearStory development environment stopped!" -ForegroundColor Green
Write-Host ""
Write-Host "💡 To start again: .\scripts\dev-start.ps1" -ForegroundColor Cyan
