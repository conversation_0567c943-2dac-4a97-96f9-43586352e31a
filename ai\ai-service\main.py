from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import g4f
import g4f.models
import logging
import os
from typing import List, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="NuclearStory AI Service",
    description="AI text generation service for NuclearStory game using g4f",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.getenv("CORS_ORIGIN", "http://localhost:3000").split(","),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class StoryRequest(BaseModel):
    prompt: str
    context: Optional[dict] = None
    max_length: Optional[int] = 1000
    temperature: Optional[float] = 0.7

class QuestRequest(BaseModel):
    quest_type: str
    player_level: int
    location: str
    context: Optional[dict] = None

class DialogueRequest(BaseModel):
    character_name: str
    character_role: str
    personality: str
    context: dict
    player_message: Optional[str] = None

class AIResponse(BaseModel):
    success: bool
    content: str
    provider_used: Optional[str] = None
    error: Optional[str] = None

class AIService:
    def __init__(self):
        self.providers = [
            g4f.Provider.Copilot,
            g4f.Provider.Bing,
            g4f.Provider.ChatgptAi,
            g4f.Provider.GptGo,
        ]
        
    async def generate_text(self, messages: List[dict], max_retries: int = 3) -> AIResponse:
        """Generate text using g4f providers with fallback"""
        
        for provider in self.providers:
            logger.info(f"Trying provider: {provider.__name__}")
            
            for attempt in range(max_retries):
                try:
                    response = await g4f.ChatCompletion.create_async(
                        provider=provider,
                        model="gpt-4",
                        messages=messages,
                        stream=False
                    )
                    
                    if response and len(response.strip()) > 0:
                        logger.info(f"Success with provider: {provider.__name__}")
                        return AIResponse(
                            success=True,
                            content=response.strip(),
                            provider_used=provider.__name__
                        )
                        
                except Exception as e:
                    logger.warning(f"Attempt {attempt + 1} failed with {provider.__name__}: {e}")
                    if attempt == max_retries - 1:
                        logger.error(f"All attempts failed for provider {provider.__name__}")
        
        logger.error("All providers failed")
        return AIResponse(
            success=False,
            content="",
            error="All AI providers failed to generate content"
        )

# Initialize AI service
ai_service = AIService()

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "ai-service"}

@app.post("/generate/story", response_model=AIResponse)
async def generate_story(request: StoryRequest):
    """Generate story content for the game"""
    
    system_prompt = """You are a master storyteller for a post-apocalyptic survival game called NuclearStory. 

Setting: The world has been devastated by nuclear war. Survivors live in underground bunkers or brave the radioactive wasteland above. Resources are scarce, danger is everywhere, and every decision matters for survival.

Your task is to generate immersive, engaging story content that:
- Maintains a dark but hopeful tone
- Focuses on survival themes
- Includes meaningful choices with consequences
- Creates emotional connections
- Builds tension and atmosphere
- Is written in Russian language

Generate a story segment (200-500 words) that describes the current situation vividly and presents 2-3 meaningful choices with clear potential consequences."""

    context_info = ""
    if request.context:
        context_info = f"""
Player Context:
- Location: {request.context.get('location', 'unknown')}
- Health: {request.context.get('health', 100)}%
- Hunger: {request.context.get('hunger', 100)}%
- Thirst: {request.context.get('thirst', 100)}%
- Radiation: {request.context.get('radiation', 0)}%
- Inventory: {request.context.get('inventory', [])}
- Current Quest: {request.context.get('quest', 'none')}
"""

    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": f"{context_info}\n\nPrompt: {request.prompt}"}
    ]
    
    return await ai_service.generate_text(messages)

@app.post("/generate/quest", response_model=AIResponse)
async def generate_quest(request: QuestRequest):
    """Generate quest content"""
    
    system_prompt = f"""Generate a quest for NuclearStory post-apocalyptic survival game.

Quest Type: {request.quest_type}
Player Level: {request.player_level}
Location: {request.location}

Create a quest with:
- Compelling title
- Clear description and objectives
- Appropriate rewards
- Realistic challenges
- Written in Russian language

Format as JSON with fields: title, description, objectives, rewards, estimated_duration"""

    context_info = ""
    if request.context:
        context_info = f"Additional context: {request.context}"

    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": f"Generate a {request.quest_type} quest. {context_info}"}
    ]
    
    return await ai_service.generate_text(messages)

@app.post("/generate/dialogue", response_model=AIResponse)
async def generate_dialogue(request: DialogueRequest):
    """Generate NPC dialogue"""
    
    system_prompt = f"""Create dialogue for an NPC in NuclearStory.

Character: {request.character_name}
Role: {request.character_role}
Personality: {request.personality}

Generate natural dialogue that:
- Reflects the character's personality
- Fits the post-apocalyptic setting
- Uses appropriate wasteland terminology
- Is written in Russian language
- Includes 2-3 dialogue options for the player

Context: {request.context}"""

    user_message = request.player_message or "Start conversation"
    
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": f"Player says: {user_message}"}
    ]
    
    return await ai_service.generate_text(messages)

@app.post("/generate/event", response_model=AIResponse)
async def generate_event(request: StoryRequest):
    """Generate random event description"""
    
    system_prompt = """Generate a random event description for NuclearStory wasteland.

Create an atmospheric description (100-200 words) that:
- Sets a vivid scene
- Creates appropriate tension
- Includes sensory details
- Hints at dangers or opportunities
- Maintains post-apocalyptic atmosphere
- Ends with a choice or decision point
- Is written in Russian language"""

    context_info = ""
    if request.context:
        context_info = f"""
Event Context:
- Location: {request.context.get('location', 'wasteland')}
- Danger Level: {request.context.get('danger_level', 'medium')}
- Player Health: {request.context.get('health', 100)}%
- Radiation: {request.context.get('radiation', 0)}%
"""

    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": f"{context_info}\n\nEvent type: {request.prompt}"}
    ]
    
    return await ai_service.generate_text(messages)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=int(os.getenv("PORT", 3005)),
        reload=os.getenv("NODE_ENV") == "development"
    )
