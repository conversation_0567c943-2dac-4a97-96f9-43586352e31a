import { NPCType, FactionAlignment } from '../enums'
import { Position, Special } from '../models/Player'
import { InventoryItem } from '../models/InventoryItem'

export interface NPCDialogue {
  id: string
  text: string
  conditions?: string[] // Quest IDs, item requirements, etc.
  responses: NPCDialogueResponse[]
}

export interface NPCDialogueResponse {
  id: string
  text: string
  action?: 'trade' | 'quest' | 'info' | 'combat' | 'leave'
  requirements?: {
    items?: string[] // Item IDs
    skills?: Record<string, number>
    reputation?: number
    currency?: number
  }
  consequences?: {
    addItems?: string[] // Item IDs
    removeItems?: string[] // Item IDs
    addQuest?: string // Quest ID
    changeReputation?: number
    giveCurrency?: number
    takeCurrency?: number
  }
}

export interface NPCTrade {
  buyPrices: Record<string, number> // Item ID -> price
  sellItems: InventoryItem[]
  currency: number
  tradeModifier: number // Based on charisma, reputation
}

export interface NPCSchedule {
  timeSlots: {
    startHour: number
    endHour: number
    locationId: string
    activity: string
  }[]
}

export interface NPC {
  id: string
  name: string
  description: string
  type: NPCType
  
  // Appearance
  avatar?: string
  appearance: {
    gender: 'male' | 'female' | 'other'
    age: number
    height: number
    build: 'thin' | 'average' | 'muscular' | 'heavy'
    hairColor: string
    eyeColor: string
    skinTone: string
    distinguishingMarks?: string[]
  }
  
  // Stats (simplified SPECIAL)
  special: Special
  level: number
  
  // Health and combat
  currentHP: number
  maxHP: number
  combatSkill: number
  isHostile: boolean
  
  // Position and movement
  position: Position
  currentLocationId: string
  homeLocationId?: string
  movementSpeed: number
  
  // Faction and relationships
  factionId?: string
  factionRank?: string
  alignment: FactionAlignment
  reputation: number // Player's reputation with this NPC
  
  // Inventory and equipment
  inventory: InventoryItem[]
  equippedWeapon?: string // Item ID
  equippedArmor?: string // Item ID
  
  // Dialogue and interaction
  dialogues: NPCDialogue[]
  currentDialogueId?: string
  hasSpokenToPlayer: boolean
  lastInteractionAt?: Date
  
  // Trading
  isTrader: boolean
  trade?: NPCTrade
  
  // Behavior and AI
  personality: {
    aggression: number // 0-100
    friendliness: number // 0-100
    intelligence: number // 0-100
    courage: number // 0-100
    greed: number // 0-100
  }
  
  // Schedule and routine
  schedule?: NPCSchedule
  currentActivity: string
  
  // Quests
  availableQuests: string[] // Quest IDs
  completedQuests: string[] // Quest IDs
  
  // Status
  isAlive: boolean
  isEssential: boolean // Cannot be killed
  isUnique: boolean // Only one instance
  
  // Timestamps
  createdAt: Date
  lastSeenAt: Date
  respawnAt?: Date // If killed and can respawn
}
