version: '3.8'

services:
  # Databases
  postgres-auth:
    image: postgres:15-alpine
    container_name: nuclearstory-postgres-auth
    environment:
      POSTGRES_DB: auth_db
      POSTGRES_USER: nuclearstory
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_auth_data:/var/lib/postgresql/data
    networks:
      - nuclearstory-network

  postgres-saves:
    image: postgres:15-alpine
    container_name: nuclearstory-postgres-saves
    environment:
      POSTGRES_DB: saves_db
      POSTGRES_USER: nuclearstory
      POSTGRES_PASSWORD: password
    ports:
      - "5433:5432"
    volumes:
      - postgres_saves_data:/var/lib/postgresql/data
    networks:
      - nuclearstory-network

  redis:
    image: redis:7-alpine
    container_name: nuclearstory-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - nuclearstory-network

  # Backend Services
  auth-service:
    build:
      context: ../back/auth-service
      dockerfile: Dockerfile
    container_name: nuclearstory-auth-service
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - DATABASE_HOST=postgres-auth
      - DATABASE_PORT=5432
      - DATABASE_USERNAME=nuclearstory
      - DATABASE_PASSWORD=password
      - DATABASE_NAME=auth_db
      - JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
      - JWT_EXPIRES_IN=7d
      - CORS_ORIGIN=http://localhost:3000
    depends_on:
      - postgres-auth
    networks:
      - nuclearstory-network
    restart: unless-stopped

  game-engine-service:
    build:
      context: ../back/game-engine-service
      dockerfile: Dockerfile
    container_name: nuclearstory-game-engine-service
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=development
      - PORT=3002
      - AUTH_SERVICE_URL=http://auth-service:3001
      - STORY_SERVICE_URL=http://story-service:3003
      - SAVE_SERVICE_URL=http://save-service:3004
      - CORS_ORIGIN=http://localhost:3000
    depends_on:
      - auth-service
    networks:
      - nuclearstory-network
    restart: unless-stopped

  ai-service:
    build:
      context: ../ai/ai-service
      dockerfile: Dockerfile
    container_name: nuclearstory-ai-service
    ports:
      - "3005:3005"
    environment:
      - NODE_ENV=development
      - PORT=3005
      - CORS_ORIGIN=http://localhost:3000,http://localhost:80
      - LOG_LEVEL=INFO
    networks:
      - nuclearstory-network
    restart: unless-stopped

  story-service:
    build:
      context: ../back/story-service
      dockerfile: Dockerfile
    container_name: nuclearstory-story-service
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=development
      - PORT=3003
      - AUTH_SERVICE_URL=http://auth-service:3001
      - AI_SERVICE_URL=http://ai-service:3005
      - CORS_ORIGIN=http://localhost:3000
    depends_on:
      - auth-service
      - ai-service
      - redis
    networks:
      - nuclearstory-network
    restart: unless-stopped

  save-service:
    build:
      context: ../back/save-service
      dockerfile: Dockerfile
    container_name: nuclearstory-save-service
    ports:
      - "3004:3004"
    environment:
      - NODE_ENV=development
      - PORT=3004
      - DATABASE_HOST=postgres-saves
      - DATABASE_PORT=5432
      - DATABASE_USERNAME=nuclearstory
      - DATABASE_PASSWORD=password
      - DATABASE_NAME=saves_db
      - AUTH_SERVICE_URL=http://auth-service:3001
      - CORS_ORIGIN=http://localhost:3000
    depends_on:
      - postgres-saves
      - auth-service
    networks:
      - nuclearstory-network
    restart: unless-stopped

  # Frontend
  frontend:
    build:
      context: ../front
      dockerfile: Dockerfile
    container_name: nuclearstory-frontend
    ports:
      - "3000:80"
    environment:
      - VITE_API_URL=http://localhost:80/api
      - VITE_AUTH_SERVICE_URL=http://localhost:80/api/auth
      - VITE_GAME_SERVICE_URL=http://localhost:80/api/game
      - VITE_STORY_SERVICE_URL=http://localhost:80/api/story
      - VITE_SAVE_SERVICE_URL=http://localhost:80/api/save
    networks:
      - nuclearstory-network

  # Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: nuclearstory-nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - auth-service
      - game-engine-service
      - story-service
      - save-service
    networks:
      - nuclearstory-network
    restart: unless-stopped

volumes:
  postgres_auth_data:
  postgres_saves_data:
  redis_data:

networks:
  nuclearstory-network:
    driver: bridge
