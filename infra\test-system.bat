@echo off
REM NuclearStory - System Test Script for Windows
echo 🧪 Testing NuclearStory system...

REM Check if Docker is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running. Please start Docker Desktop first.
    pause
    exit /b 1
)

echo 📊 Checking container status...
docker compose -f docker-compose.dev.yml ps

echo.
echo 🌐 Testing service endpoints...

REM Function to test URL (using curl if available, otherwise skip)
curl --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Testing Frontend...
    curl -s -o nul -w "Frontend: %%{http_code}\n" http://localhost:3000
    
    echo Testing Auth Service...
    curl -s -o nul -w "Auth Service: %%{http_code}\n" http://localhost:3001/health
    
    echo Testing Game Engine...
    curl -s -o nul -w "Game Engine: %%{http_code}\n" http://localhost:3002/health
    
    echo Testing Story Service...
    curl -s -o nul -w "Story Service: %%{http_code}\n" http://localhost:3003/health
    
    echo Testing Save Service...
    curl -s -o nul -w "Save Service: %%{http_code}\n" http://localhost:3004/health
    
    echo Testing AI Service...
    curl -s -o nul -w "AI Service: %%{http_code}\n" http://localhost:3005/health
    
    echo Testing Nginx...
    curl -s -o nul -w "Nginx: %%{http_code}\n" http://localhost:80
) else (
    echo ⚠️  curl not found. Install curl or Git for Windows to test endpoints.
    echo You can manually test these URLs in browser:
    echo   - http://localhost:3000 (Frontend)
    echo   - http://localhost:80 (API Gateway)
)

echo.
echo 📋 Container logs (last 10 lines each):
echo.

echo === Frontend ===
docker compose -f docker-compose.dev.yml logs --tail=5 frontend

echo.
echo === Auth Service ===
docker compose -f docker-compose.dev.yml logs --tail=5 auth-service

echo.
echo === Game Engine ===
docker compose -f docker-compose.dev.yml logs --tail=5 game-engine-service

echo.
echo 🎯 Quick Access:
echo   🎮 Game: http://localhost:3000
echo   🌍 API: http://localhost:80
echo   📚 Docs: http://localhost:3001/api/docs
echo.
echo 📊 Full logs: docker compose -f docker-compose.dev.yml logs -f
echo.
pause
