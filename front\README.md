# Frontend - NuclearStory

React + Vite frontend application for the NuclearStory game.

## Tech Stack

- **React 18** with TypeScript
- **Vite** for build tooling and dev server
- **React Router** for client-side routing
- **Zustand** for state management
- **Tailwind CSS** for styling
- **shadcn/ui** for UI components
- **Axios** for API communication

## Getting Started

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## Project Structure

```
front/
├── src/
│   ├── components/     # Reusable UI components
│   ├── pages/         # Page components
│   ├── hooks/         # Custom React hooks
│   ├── store/         # Zustand stores
│   ├── services/      # API services
│   ├── types/         # TypeScript type definitions
│   ├── utils/         # Utility functions
│   └── styles/        # Global styles
├── public/            # Static assets
└── index.html         # Entry HTML file
```

## Available Routes

- `/` - Home page
- `/login` - User login
- `/signup` - User registration
- `/game` - Main game interface

## Development

The application uses hot module replacement (HMR) for fast development.
All changes will be reflected immediately in the browser.
