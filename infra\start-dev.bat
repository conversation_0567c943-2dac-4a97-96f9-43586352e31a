@echo off
REM NuclearStory - Development Start Script for Windows
echo 🛠️  Starting NuclearStory in DEVELOPMENT mode with hot reload...

REM Check if Docker is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running. Please start Docker Desktop first.
    pause
    exit /b 1
)

REM Check if .env file exists
if not exist ".env" (
    echo ⚠️  .env file not found. Creating default .env file...
    (
        echo # NuclearStory Development Environment
        echo NODE_ENV=development
        echo POSTGRES_PASSWORD=password
        echo POSTGRES_USER=nuclearstory
        echo JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
        echo JWT_EXPIRES_IN=7d
        echo CORS_ORIGIN=http://localhost:3000
        echo LOG_LEVEL=INFO
    ) > .env
    echo 📝 .env file created with default values for development.
)

echo 🔧 Building development images...
docker compose -f docker-compose.dev.yml build

echo 🗄️  Starting databases and Redis...
docker compose -f docker-compose.dev.yml up -d postgres-auth postgres-saves redis

echo ⏳ Waiting for databases to be ready...
timeout /t 15 /nobreak >nul

echo 🤖 Starting AI service...
docker compose -f docker-compose.dev.yml up -d ai-service

echo ⏳ Waiting for AI service...
timeout /t 10 /nobreak >nul

echo 🔧 Starting backend services with hot reload...
docker compose -f docker-compose.dev.yml up -d auth-service

echo ⏳ Waiting for auth service...
timeout /t 15 /nobreak >nul

echo 🔧 Starting remaining backend services...
docker compose -f docker-compose.dev.yml up -d game-engine-service story-service save-service

echo ⏳ Waiting for backend services...
timeout /t 15 /nobreak >nul

echo 🎨 Starting frontend with hot reload...
docker compose -f docker-compose.dev.yml up -d frontend

echo 🌐 Starting nginx proxy...
docker compose -f docker-compose.dev.yml up -d nginx

echo.
echo ✅ NuclearStory development environment is ready!
echo.
echo 🌐 Access points:
echo    🎮 Game (Frontend): http://localhost:3000
echo    🌍 API Gateway: http://localhost:80
echo    🔐 Auth Service: http://localhost:3001/api/docs
echo    🎯 Game Engine: http://localhost:3002/api/docs
echo    📖 Story Service: http://localhost:3003/api/docs
echo    💾 Save Service: http://localhost:3004/api/docs
echo    🤖 AI Service: http://localhost:3005/docs
echo.
echo 🔥 HOT RELOAD ENABLED:
echo    - Edit files in /front, /back, /ai folders
echo    - Changes will be automatically reflected
echo    - No need to rebuild containers
echo.
echo 📊 Monitor with: docker compose -f docker-compose.dev.yml logs -f
echo 📈 Check status: docker compose -f docker-compose.dev.yml ps
echo 🛑 Stop with: docker compose -f docker-compose.dev.yml down
echo.
pause
