import { FactionAlignment } from '../enums'

export interface FactionRank {
  id: string
  name: string
  description: string
  level: number
  requirements: {
    reputation: number
    completedQuests?: string[] // Quest IDs
    skills?: Record<string, number>
    items?: string[] // Item IDs required
  }
  benefits: {
    discountPercentage?: number
    accessToAreas?: string[] // Location IDs
    specialItems?: string[] // Item IDs
    abilities?: string[] // Special ability IDs
  }
}

export interface FactionTerritory {
  locationIds: string[] // Controlled locations
  influenceMap: Record<string, number> // Location ID -> influence level (0-100)
  borderLocations: string[] // Contested or border locations
  strategicLocations: string[] // Important locations for faction
}

export interface FactionRelationship {
  factionId: string
  relationshipType: 'allied' | 'friendly' | 'neutral' | 'hostile' | 'enemy'
  relationshipValue: number // -100 to 100
  isAtWar: boolean
  tradeAgreement: boolean
  nonAggressionPact: boolean
  lastConflictAt?: Date
}

export interface FactionQuest {
  questId: string
  isRepeatable: boolean
  minimumRank: number
  reputationReward: number
  isAvailable: boolean
}

export interface FactionResource {
  type: string
  amount: number
  productionRate: number // per day
  consumptionRate: number // per day
  tradable: boolean
}

export interface Faction {
  id: string
  name: string
  description: string
  shortName: string // Abbreviation
  
  // Basic properties
  alignment: FactionAlignment
  ideology: string
  goals: string[]
  
  // Leadership and structure
  leaderId?: string // NPC ID
  leaderTitle: string
  governmentType: 'democracy' | 'autocracy' | 'oligarchy' | 'anarchy' | 'technocracy'
  
  // Membership and ranks
  ranks: FactionRank[]
  memberCount: number
  recruitmentOpen: boolean
  recruitmentRequirements?: {
    minimumLevel?: number
    skills?: Record<string, number>
    items?: string[] // Item IDs
    questsCompleted?: string[] // Quest IDs
  }
  
  // Territory and influence
  territory: FactionTerritory
  totalInfluence: number
  isExpanding: boolean
  
  // Relationships
  relationships: FactionRelationship[]
  
  // Player relationship
  playerReputation: number // -100 to 100
  playerRank?: string // Rank ID
  playerJoinedAt?: Date
  
  // Economy and resources
  resources: FactionResource[]
  currency: number
  tradeRoutes: string[] // Trade route IDs
  
  // Military and security
  militaryStrength: number
  defenseRating: number
  aggressionLevel: number // 0-100
  
  // Quests and missions
  availableQuests: FactionQuest[]
  
  // Technology and capabilities
  technologyLevel: number // 0-100
  specialAbilities: string[] // Special faction abilities
  uniqueItems: string[] // Item IDs only this faction has
  
  // Culture and appearance
  colors: {
    primary: string
    secondary: string
  }
  symbol?: string
  uniform?: string // Item ID
  architecture?: string
  
  // Lore and background
  foundedYear?: number
  founderName?: string
  history: string
  motto?: string
  
  // Status and flags
  isActive: boolean
  isPlayable: boolean // Can player join
  isHostileToPlayer: boolean
  isEssential: boolean // Cannot be destroyed
  
  // Events and activities
  currentEvents: string[] // Event IDs
  scheduledEvents: {
    eventId: string
    scheduledAt: Date
  }[]
  
  // Timestamps
  createdAt: Date
  lastActivityAt: Date
  lastWarAt?: Date
}
