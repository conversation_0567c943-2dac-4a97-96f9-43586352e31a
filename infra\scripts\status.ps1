# NuclearStory - Status Check Script (PowerShell)
Write-Host "📈 Checking NuclearStory services status..." -ForegroundColor Cyan

# Check if Docker is running
try {
    docker info | Out-Null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker not running"
    }
} catch {
    Write-Host "❌ Docker is not running." -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🐳 Docker Containers Status:" -ForegroundColor Green
docker compose -f docker-compose.dev.yml ps

Write-Host ""
Write-Host "🌐 Service Health Check:" -ForegroundColor Green

# Function to check service health
function Test-ServiceHealth {
    param(
        [string]$Url,
        [string]$ServiceName
    )
    
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "   ✅ $ServiceName - OK" -ForegroundColor Green
        } else {
            Write-Host "   ⚠️  $ServiceName - Status: $($response.StatusCode)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "   ❌ $ServiceName - Not responding" -ForegroundColor Red
    }
}

# Check services
Test-ServiceHealth "http://localhost:3000" "Frontend"
Test-ServiceHealth "http://localhost:3001/health" "Auth Service"
Test-ServiceHealth "http://localhost:3002/health" "Game Engine"
Test-ServiceHealth "http://localhost:3003/health" "Story Service"
Test-ServiceHealth "http://localhost:3004/health" "Save Service"
Test-ServiceHealth "http://localhost:3005/health" "AI Service"
Test-ServiceHealth "http://localhost:80" "Nginx Proxy"

Write-Host ""
Write-Host "🔗 Quick Access Links:" -ForegroundColor Cyan
Write-Host "   🎮 Game: http://localhost:3000" -ForegroundColor White
Write-Host "   🌍 API: http://localhost:80" -ForegroundColor White
Write-Host "   📚 Docs: http://localhost:3001/api/docs" -ForegroundColor White
