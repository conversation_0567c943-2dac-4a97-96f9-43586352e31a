# AI Integration - NuclearStory

This directory contains AI models, prompts, and integrations for the NuclearStory game.

## Structure

### `/models`
- Custom AI model configurations
- Fine-tuned model parameters
- Model evaluation scripts

### `/prompts`
- Story generation prompts
- Character dialogue templates
- Quest generation prompts
- Event description templates

### `/integrations`
- OpenAI API integration
- Hugging Face integration
- Anthropic Claude integration
- Custom model serving

### `/utils`
- AI response processing utilities
- Content moderation tools
- Prompt engineering helpers

## Supported AI Services

### OpenAI GPT
- Story generation
- Dynamic dialogue
- Quest creation
- Event descriptions

### Hugging Face
- Open-source model alternatives
- Custom fine-tuned models
- Specialized gaming models

### Anthropic Claude
- Advanced reasoning for complex scenarios
- Ethical content generation
- Long-form narrative creation

## Usage

The AI services are primarily used by the `story-service` microservice to:

1. **Generate Dynamic Stories**: Create unique narratives based on player choices
2. **Create Quests**: Generate procedural quests and objectives
3. **Develop Characters**: Create NPCs with unique personalities
4. **Describe Events**: Generate immersive event descriptions
5. **Moderate Content**: Ensure appropriate content generation

## Configuration

AI services are configured through environment variables in each service's `.env` file:

```env
OPENAI_API_KEY=your-key-here
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=1000
OPENAI_TEMPERATURE=0.7
```

## Future Enhancements

- Local AI model deployment
- Custom fine-tuned models for post-apocalyptic content
- Real-time AI-driven NPCs
- Procedural world generation
- AI-powered game balancing
