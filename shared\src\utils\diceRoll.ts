export interface DiceRollResult {
  result: number
  rolls: number[]
  modifier: number
  total: number
  isCriticalSuccess: boolean
  isCriticalFailure: boolean
  isSuccess: boolean
  difficulty?: number
}

/**
 * Roll a single die with specified number of sides
 */
export function rollDie(sides: number): number {
  return Math.floor(Math.random() * sides) + 1
}

/**
 * Roll multiple dice and return individual results
 */
export function rollDice(count: number, sides: number): number[] {
  const rolls: number[] = []
  for (let i = 0; i < count; i++) {
    rolls.push(rollDie(sides))
  }
  return rolls
}

/**
 * Roll dice with modifier and return detailed result
 */
export function rollWithModifier(
  count: number,
  sides: number,
  modifier: number = 0,
  difficulty?: number
): DiceRollResult {
  const rolls = rollDice(count, sides)
  const result = rolls.reduce((sum, roll) => sum + roll, 0)
  const total = result + modifier
  
  // Determine critical success/failure (for d20 system)
  const isCriticalSuccess = sides === 20 && rolls.some(roll => roll === 20)
  const isCriticalFailure = sides === 20 && rolls.some(roll => roll === 1)
  
  // Determine success if difficulty is provided
  const isSuccess = difficulty !== undefined ? total >= difficulty : false
  
  return {
    result,
    rolls,
    modifier,
    total,
    isCriticalSuccess,
    isCriticalFailure,
    isSuccess,
    difficulty
  }
}

/**
 * Standard d20 roll with modifier
 */
export function d20(modifier: number = 0, difficulty?: number): DiceRollResult {
  return rollWithModifier(1, 20, modifier, difficulty)
}

/**
 * Standard d100 roll (percentile)
 */
export function d100(modifier: number = 0, difficulty?: number): DiceRollResult {
  return rollWithModifier(1, 100, modifier, difficulty)
}

/**
 * Roll for damage (e.g., 2d6+3)
 */
export function rollDamage(count: number, sides: number, modifier: number = 0): number {
  const result = rollWithModifier(count, sides, modifier)
  return Math.max(1, result.total) // Minimum 1 damage
}

/**
 * Roll for skill check with SPECIAL stat modifier
 */
export function skillCheck(
  skillLevel: number,
  statValue: number,
  difficulty: number,
  modifier: number = 0
): DiceRollResult {
  const totalModifier = skillLevel + statValue + modifier
  return d20(totalModifier, difficulty)
}

/**
 * Roll for attribute check (pure SPECIAL stat)
 */
export function attributeCheck(
  statValue: number,
  difficulty: number,
  modifier: number = 0
): DiceRollResult {
  return d20(statValue + modifier, difficulty)
}

/**
 * Roll for luck-based events
 */
export function luckRoll(luckValue: number, baseChance: number = 50): boolean {
  const roll = d100()
  const adjustedChance = baseChance + (luckValue - 5) * 5 // Luck 5 is average
  return roll.total <= adjustedChance
}

/**
 * Roll for random encounter
 */
export function encounterRoll(encounterChance: number): boolean {
  const roll = d100()
  return roll.total <= encounterChance
}

/**
 * Roll for critical hit
 */
export function criticalHitRoll(criticalChance: number, luckModifier: number = 0): boolean {
  const adjustedChance = criticalChance + luckModifier
  const roll = d100()
  return roll.total <= adjustedChance
}

/**
 * Roll for item condition/durability loss
 */
export function durabilityRoll(currentDurability: number, maxDurability: number): number {
  const conditionPercent = (currentDurability / maxDurability) * 100
  const roll = d100()
  
  // Better condition = less likely to degrade
  if (roll.total > conditionPercent) {
    return Math.max(0, currentDurability - rollDie(3))
  }
  
  return currentDurability
}

/**
 * Roll for random loot generation
 */
export function lootRoll(lootChance: number, qualityModifier: number = 0): {
  hasLoot: boolean
  quality: 'poor' | 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary'
} {
  const roll = d100()
  const hasLoot = roll.total <= lootChance
  
  if (!hasLoot) {
    return { hasLoot: false, quality: 'poor' }
  }
  
  // Determine quality based on second roll + modifier
  const qualityRoll = d100(qualityModifier)
  let quality: 'poor' | 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary'
  
  if (qualityRoll.total >= 95) quality = 'legendary'
  else if (qualityRoll.total >= 85) quality = 'epic'
  else if (qualityRoll.total >= 70) quality = 'rare'
  else if (qualityRoll.total >= 50) quality = 'uncommon'
  else if (qualityRoll.total >= 25) quality = 'common'
  else quality = 'poor'
  
  return { hasLoot, quality }
}
