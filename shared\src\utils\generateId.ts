/**
 * Generate a UUID v4 (random)
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

/**
 * Generate a short random ID (nanoid style)
 */
export function generateShortId(length: number = 8): string {
  const alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
  let result = ''
  
  for (let i = 0; i < length; i++) {
    result += alphabet.charAt(Math.floor(Math.random() * alphabet.length))
  }
  
  return result
}

/**
 * Generate a prefixed ID for specific entity types
 */
export function generatePrefixedId(prefix: string, length: number = 8): string {
  return `${prefix}_${generateShortId(length)}`
}

/**
 * Generate IDs for specific game entities
 */
export const generatePlayerId = (): string => generatePrefixedId('player')
export const generateNPCId = (): string => generatePrefixedId('npc')
export const generateLocationId = (): string => generatePrefixedId('loc')
export const generateItemId = (): string => generatePrefixedId('item')
export const generateQuestId = (): string => generatePrefixedId('quest')
export const generateEventId = (): string => generatePrefixedId('event')
export const generateFactionId = (): string => generatePrefixedId('faction')
export const generateBattleId = (): string => generatePrefixedId('battle')
export const generateStructureId = (): string => generatePrefixedId('struct')
export const generateSaveId = (): string => generatePrefixedId('save')

/**
 * Generate a timestamp-based ID
 */
export function generateTimestampId(prefix?: string): string {
  const timestamp = Date.now().toString(36)
  const random = Math.random().toString(36).substr(2, 5)
  const id = `${timestamp}${random}`
  
  return prefix ? `${prefix}_${id}` : id
}

/**
 * Generate a sequential ID with counter
 */
export class SequentialIdGenerator {
  private counter: number = 0
  private prefix: string
  
  constructor(prefix: string = '', startFrom: number = 1) {
    this.prefix = prefix
    this.counter = startFrom - 1
  }
  
  next(): string {
    this.counter++
    return this.prefix ? `${this.prefix}_${this.counter}` : this.counter.toString()
  }
  
  current(): number {
    return this.counter
  }
  
  reset(startFrom: number = 1): void {
    this.counter = startFrom - 1
  }
}

/**
 * Generate a human-readable ID with words
 */
export function generateReadableId(): string {
  const adjectives = [
    'brave', 'swift', 'mighty', 'clever', 'silent', 'fierce', 'noble', 'wise',
    'bold', 'quick', 'strong', 'sharp', 'bright', 'dark', 'wild', 'calm'
  ]
  
  const nouns = [
    'wolf', 'eagle', 'bear', 'fox', 'hawk', 'lion', 'tiger', 'dragon',
    'warrior', 'hunter', 'scout', 'guardian', 'ranger', 'knight', 'sage', 'wanderer'
  ]
  
  const adjective = adjectives[Math.floor(Math.random() * adjectives.length)]
  const noun = nouns[Math.floor(Math.random() * nouns.length)]
  const number = Math.floor(Math.random() * 1000)
  
  return `${adjective}_${noun}_${number}`
}

/**
 * Validate if a string is a valid UUID
 */
export function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidRegex.test(uuid)
}

/**
 * Validate if a string is a valid prefixed ID
 */
export function isValidPrefixedId(id: string, expectedPrefix?: string): boolean {
  const parts = id.split('_')
  if (parts.length !== 2) return false
  
  if (expectedPrefix && parts[0] !== expectedPrefix) return false
  
  // Check if the second part is alphanumeric
  const idPart = parts[1]
  return /^[a-zA-Z0-9]+$/.test(idPart)
}

/**
 * Extract prefix from a prefixed ID
 */
export function extractPrefix(id: string): string | null {
  const parts = id.split('_')
  return parts.length === 2 ? parts[0] : null
}

/**
 * Extract ID part from a prefixed ID
 */
export function extractIdPart(id: string): string | null {
  const parts = id.split('_')
  return parts.length === 2 ? parts[1] : null
}
