# 🚀 NuclearStory - Быстрый старт на Windows

## 📋 Что нужно

1. **Docker Desktop** - скачать с [docker.com](https://www.docker.com/products/docker-desktop/)
2. **Git** - скачать с [git-scm.com](https://git-scm.com/)

## ⚡ Запуск за 3 шага

### 1. Клонируйте репозиторий
```cmd
git clone <your-repo-url>
cd NuclearStory
```

### 2. Запустите Docker Desktop
Убедитесь, что Docker Desktop запущен и работает.

### 3. Запустите игру
```cmd
cd infra
start-dev.bat
```

**Готово!** 🎉

## 🌐 Доступ к игре

После запуска откройте в браузере:
- **🎮 Игра**: http://localhost:3000
- **🌍 API**: http://localhost:80

## 🛑 Остановка

```cmd
cd infra
stop-dev.bat
```

## 🔧 Если что-то не работает

### Проблема: Docker не запускается
**Решение**: Убедитесь, что Docker Desktop запущен

### Проблема: Порты заняты
**Решение**: 
```cmd
netstat -ano | findstr :3000
# Найдите PID процесса и завершите его
taskkill /PID <PID> /F
```

### Проблема: Контейнеры не собираются
**Решение**: Очистите Docker
```cmd
cd infra
docker compose -f docker-compose.dev.yml down -v
docker system prune -f
start-dev.bat
```

## 🔥 Hot Reload

Все изменения в коде применяются автоматически:
- Редактируйте файлы в `front/`, `back/`, `ai/`
- Сохраняйте файлы
- Изменения появятся в браузере автоматически!

## 📊 Мониторинг

```cmd
# Статус контейнеров
docker compose -f docker-compose.dev.yml ps

# Логи всех сервисов
docker compose -f docker-compose.dev.yml logs -f

# Логи конкретного сервиса
docker compose -f docker-compose.dev.yml logs -f frontend

# Тест системы
test-system.bat
```

## 🎯 Структура проекта

```
NuclearStory/
├── front/              # React фронтенд (порт 3000)
├── back/               # Backend сервисы
│   ├── auth-service/   # Аутентификация (порт 3001)
│   ├── game-engine-service/  # Игровая логика (порт 3002)
│   ├── story-service/  # AI истории (порт 3003)
│   └── save-service/   # Сохранения (порт 3004)
├── ai/                 # AI сервисы (порт 3005)
├── infra/              # Docker конфигурация
└── shared/             # Общие типы
```

## 💡 Полезные команды

```cmd
# Перезапуск конкретного сервиса
docker compose -f docker-compose.dev.yml restart auth-service

# Вход в контейнер
docker compose -f docker-compose.dev.yml exec auth-service sh

# Просмотр использования ресурсов
docker stats

# Полная очистка
docker compose -f docker-compose.dev.yml down -v
docker system prune -a -f
```

---

**Удачной разработки!** 🚀

Если возникли вопросы, смотрите подробную документацию в [README.md](README.md) или [infra/WINDOWS-SETUP.md](infra/WINDOWS-SETUP.md)
