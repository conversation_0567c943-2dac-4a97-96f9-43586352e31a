import { InventoryItem } from './InventoryItem'
import { EffectType } from '../enums'

export interface Effect {
  id: string
  type: EffectType
  duration: number
  value: number
  description: string
}

export interface Special {
  S: number // Strength
  P: number // Perception
  E: number // Endurance
  C: number // Charisma
  I: number // Intelligence
  A: number // Agility
  L: number // Luck
}

export interface Position {
  x: number
  y: number
}

export interface Player {
  id: string
  name: string
  level: number
  experience: number
  experienceToNext: number
  
  // SPECIAL stats
  special: Special
  
  // Health and status
  currentHP: number
  maxHP: number
  currentAP: number
  maxAP: number
  radiationLevel: number
  
  // Needs
  hunger: number
  thirst: number
  fatigue: number
  
  // Skills and perks
  perks: string[]
  skills: Record<string, number>
  
  // Inventory and equipment
  inventory: InventoryItem[]
  equippedWeapon?: string // Item ID
  equippedArmor?: string // Item ID
  
  // Position and location
  position: Position
  currentLocationId?: string
  
  // Status effects
  effects: Effect[]
  
  // Game progress
  completedQuests: string[]
  activeQuests: string[]
  discoveredLocations: string[]
  
  // Faction relations
  factionReputation: Record<string, number>
  
  // Timestamps
  createdAt: Date
  lastSaveAt: Date
}
