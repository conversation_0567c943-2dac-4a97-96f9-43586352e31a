name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '22'

jobs:
  test-shared:
    name: Test Shared Package
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: shared/package-lock.json
      
      - name: Install dependencies
        working-directory: ./shared
        run: npm ci
      
      - name: Build shared package
        working-directory: ./shared
        run: npm run build
      
      - name: Upload shared build
        uses: actions/upload-artifact@v4
        with:
          name: shared-build
          path: shared/dist

  test-frontend:
    name: Test Frontend
    runs-on: ubuntu-latest
    needs: test-shared
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: front/package-lock.json
      
      - name: Download shared build
        uses: actions/download-artifact@v4
        with:
          name: shared-build
          path: shared/dist
      
      - name: Install dependencies
        working-directory: ./front
        run: npm ci
      
      - name: Lint
        working-directory: ./front
        run: npm run lint
      
      - name: Build
        working-directory: ./front
        run: npm run build
      
      - name: Upload frontend build
        uses: actions/upload-artifact@v4
        with:
          name: frontend-build
          path: front/dist

  test-backend:
    name: Test Backend Services
    runs-on: ubuntu-latest
    needs: test-shared
    strategy:
      matrix:
        service: [auth-service, game-engine-service, story-service, save-service]
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: password
          POSTGRES_USER: nuclearstory
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: back/${{ matrix.service }}/package-lock.json
      
      - name: Download shared build
        uses: actions/download-artifact@v4
        with:
          name: shared-build
          path: shared/dist
      
      - name: Install dependencies
        working-directory: ./back/${{ matrix.service }}
        run: npm ci
      
      - name: Lint
        working-directory: ./back/${{ matrix.service }}
        run: npm run lint
        continue-on-error: true
      
      - name: Build
        working-directory: ./back/${{ matrix.service }}
        run: npm run build
      
      - name: Test
        working-directory: ./back/${{ matrix.service }}
        run: npm run test
        env:
          DATABASE_HOST: localhost
          DATABASE_PORT: 5432
          DATABASE_USERNAME: nuclearstory
          DATABASE_PASSWORD: password
          DATABASE_NAME: test_db
          REDIS_HOST: localhost
          REDIS_PORT: 6379
          JWT_SECRET: test-secret
        continue-on-error: true

  docker-build:
    name: Build Docker Images
    runs-on: ubuntu-latest
    needs: [test-frontend, test-backend]
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      
      - name: Build shared package
        working-directory: ./shared
        run: |
          npm ci
          npm run build
      
      - name: Build auth-service image
        uses: docker/build-push-action@v5
        with:
          context: ./back/auth-service
          push: false
          tags: nuclearstory/auth-service:latest
      
      - name: Build game-engine-service image
        uses: docker/build-push-action@v5
        with:
          context: ./back/game-engine-service
          push: false
          tags: nuclearstory/game-engine-service:latest
      
      - name: Build story-service image
        uses: docker/build-push-action@v5
        with:
          context: ./back/story-service
          push: false
          tags: nuclearstory/story-service:latest
      
      - name: Build save-service image
        uses: docker/build-push-action@v5
        with:
          context: ./back/save-service
          push: false
          tags: nuclearstory/save-service:latest
      
      - name: Build frontend image
        uses: docker/build-push-action@v5
        with:
          context: ./front
          push: false
          tags: nuclearstory/frontend:latest
