# NuclearStory

A post-apocalyptic survival game with AI-driven storytelling and dynamic quest generation.

## 🎮 About

NuclearStory is an interactive survival game set in a post-nuclear world where players make critical decisions that affect their survival and the story's progression. The game features AI-generated narratives, dynamic events, and a complex survival system.

## 🏗️ Architecture

This project follows a microservices architecture with the following structure:

```
nuclearstory/
├── front/              # React + Vite frontend
├── back/               # Backend microservices
│   ├── auth-service/   # Authentication & user management
│   ├── game-engine-service/  # Game logic & survival mechanics
│   ├── story-service/  # AI story generation
│   └── save-service/   # Game saves & progress
├── infra/              # Docker, nginx, environment configs
├── docs/               # Database schemas, API specs
└── shared/             # Common models & utilities
```

## 🛠️ Tech Stack

### Frontend
- **React** with **TypeScript**
- **Vite** for build tooling
- **React Router** for navigation
- **Zustand** for state management
- **Tailwind CSS** + **shadcn/ui** for styling
- **Axios** for API communication

### Backend
- **Node.js** with **TypeScript**
- **NestJS** framework for microservices
- **PostgreSQL** for data persistence
- **TypeORM** for database management
- **JWT** for authentication
- **Docker** for containerization

### Infrastructure
- **Docker Compose** for orchestration
- **nginx** for reverse proxy
- **PostgreSQL** databases per service

## 🚀 Getting Started

### Prerequisites
- **Docker Desktop** (обязательно!)
- Git
- Node.js 22+ (опционально, для локальной разработки)

### 🚀 Quick Start (Docker Desktop)

1. **Убедитесь, что Docker Desktop запущен**

2. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd nuclearstory
   ```

3. **Запуск для разработки с HOT RELOAD**

   **🪟 Windows:**
   ```cmd
   cd infra
   start-dev.bat
   ```

   **🐧 Linux/Mac:**
   ```bash
   cd infra
   ./scripts/dev-start.sh
   ```

   **⚡ PowerShell (Windows):**
   ```powershell
   cd infra
   .\scripts\dev-start.ps1
   ```

4. **Или запуск production версии**
   ```bash
   cd infra
   cp .env.example .env
   ./scripts/build.sh
   ./scripts/start.sh
   ```

4. **Access the application**
   - Frontend: http://localhost:3000
   - API Gateway: http://localhost:80
   - Auth Service: http://localhost:3001/api/docs
   - Game Engine: http://localhost:3002/api/docs
   - Story Service: http://localhost:3003/api/docs
   - Save Service: http://localhost:3004/api/docs
   - AI Service: http://localhost:3005/docs

5. **Управление сервисами**

   **🪟 Windows (простой способ):**
   ```cmd
   start-dev.bat             # Запуск dev режима
   stop-dev.bat              # Остановка dev режима
   ```

   **🐧 Linux/Mac:**
   ```bash
   ./scripts/dev-start.sh    # Запуск dev режима
   ./scripts/dev-stop.sh     # Остановка dev режима
   ./scripts/dev-logs.sh all # Просмотр логов
   ./scripts/dev-rebuild.sh  # Пересборка
   ./scripts/status.sh       # Проверка статуса
   ```

   **⚡ PowerShell (Windows):**
   ```powershell
   .\scripts\dev-start.ps1   # Запуск dev режима
   .\scripts\dev-stop.ps1    # Остановка dev режима
   .\scripts\dev-logs.ps1    # Просмотр логов
   .\scripts\status.ps1      # Проверка статуса
   ```

### 🔥 Development с Hot Reload

**Все работает в Docker с автоматической перезагрузкой!**

**🪟 Windows пользователи:** См. подробное руководство [infra/WINDOWS-SETUP.md](infra/WINDOWS-SETUP.md)

**Что происходит:**
- ✅ Все сервисы запускаются в Docker
- ✅ Код монтируется как volume
- ✅ Изменения в коде автоматически перезагружают сервисы
- ✅ Frontend с Vite HMR
- ✅ Backend с NestJS watch mode
- ✅ AI service с uvicorn reload

**Редактируйте файлы и видите изменения мгновенно!**

## 📝 Development

### Frontend Development
```bash
cd front
npm run dev          # Start development server
npm run build        # Build for production
npm run test         # Run tests
```

### Backend Development
Each microservice can be run independently:
```bash
cd back/auth-service
npm run start:dev    # Start in development mode
```

## 🎯 Game Features

- **Survival Mechanics**: Manage hunger, thirst, radiation, and health
- **AI Storytelling**: Dynamic narrative generation based on player choices
- **Quest System**: Procedurally generated quests and events
- **Character Progression**: Skills, inventory, and relationship systems
- **Save System**: Multiple save slots with progress tracking

## 📚 API Documentation

API documentation will be available at:
- Auth Service: `http://localhost:3001/api/docs`
- Game Engine: `http://localhost:3002/api/docs`
- Story Service: `http://localhost:3003/api/docs`
- Save Service: `http://localhost:3004/api/docs`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
