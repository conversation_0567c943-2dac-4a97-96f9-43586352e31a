import { TerrainType } from '../enums'
import { Position } from '../models/Player'

export interface MapCellResource {
  type: string
  amount: number
  quality: number // 0-100
  extractionDifficulty: number // 0-100
  renewsOverTime: boolean
  renewalRate?: number // per hour
}

export interface MapCellStructure {
  id: string
  type: string
  name: string
  isRuined: boolean
  canEnter: boolean
  entrancePosition?: Position
}

export interface MapCellEvent {
  id: string
  type: 'encounter' | 'discovery' | 'hazard' | 'treasure'
  probability: number // 0-100
  isTriggered: boolean
  triggerConditions?: {
    timeOfDay?: 'day' | 'night'
    weather?: string
    playerLevel?: number
    questActive?: string
  }
}

export interface MapCell {
  // Coordinates of the cell
  coord: Position 
  
  // Terrain and environment
  terrain: TerrainType
  elevation: number
  temperature: number
  radiationLevel: number
  
  // Visibility and exploration
  isVisible: boolean
  isExplored: boolean
  fogOfWar: boolean
  lastVisitedAt?: Date
  
  // Location and structures
  hasLocation: boolean
  locationId?: string
  structures: MapCellStructure[]
  
  // Movement and accessibility
  isPassable: boolean
  movementCost: number
  requiresSpecialEquipment?: string // Item type needed to traverse
  
  // Resources and loot
  resources: MapCellResource[]
  hasLoot: boolean
  lootTableId?: string
  isLooted: boolean
  lootRespawnTime?: number // hours
  
  // NPCs and enemies
  npcs: string[] // NPC IDs currently in this cell
  maxNPCs: number
  enemySpawnChance: number
  enemyTypes: string[] // Enemy type IDs
  
  // Events and encounters
  events: MapCellEvent[]
  
  // Environmental effects
  weatherEffects: {
    windSpeed: number
    precipitation: number
    visibility: number
  }
  
  // Travel and routes
  roads: string[] // Road IDs passing through this cell
  tradeRoutes: string[] // Trade route IDs
  
  // Ownership and control
  controlledBy?: string // Faction ID
  isContested: boolean
  influenceLevel: Record<string, number> // Faction ID -> influence (0-100)
  
  // Special properties
  isLandmark: boolean
  landmarkName?: string
  isStrategic: boolean // Important for faction control
  defensiveBonus: number
  
  // Fast travel
  isFastTravelPoint: boolean
  fastTravelName?: string
  
  // Timestamps
  discoveredAt?: Date
  lastUpdatedAt: Date
}
