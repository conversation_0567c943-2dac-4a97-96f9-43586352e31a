import { Effect, Player, Special } from '../models/Player'
import { EffectType } from '../enums'

export interface EffectApplication {
  success: boolean
  message: string
  valueChanged: number
  effectRemoved: boolean
}

/**
 * Apply a single effect to a player
 */
export function applyEffect(player: Player, effect: Effect): EffectApplication {
  let success = false
  let message = ''
  let valueChanged = 0
  let effectRemoved = false
  
  switch (effect.type) {
    case EffectType.HEALING:
      const healAmount = Math.min(effect.value, player.maxHP - player.currentHP)
      player.currentHP += healAmount
      valueChanged = healAmount
      success = healAmount > 0
      message = success ? `Healed ${healAmount} HP` : 'Already at full health'
      break
      
    case EffectType.POISON:
      const poisonDamage = Math.min(effect.value, player.currentHP - 1) // Don't kill with poison
      player.currentHP -= poisonDamage
      valueChanged = -poisonDamage
      success = true
      message = `Took ${poisonDamage} poison damage`
      break
      
    case EffectType.RADIATION:
      player.radiationLevel += effect.value
      valueChanged = effect.value
      success = true
      message = `Radiation increased by ${effect.value}`
      break
      
    case EffectType.BUFF_STRENGTH:
      player.special.S = Math.min(10, player.special.S + effect.value)
      valueChanged = effect.value
      success = true
      message = `Strength increased by ${effect.value}`
      break
      
    case EffectType.BUFF_PERCEPTION:
      player.special.P = Math.min(10, player.special.P + effect.value)
      valueChanged = effect.value
      success = true
      message = `Perception increased by ${effect.value}`
      break
      
    case EffectType.BUFF_ENDURANCE:
      player.special.E = Math.min(10, player.special.E + effect.value)
      valueChanged = effect.value
      success = true
      message = `Endurance increased by ${effect.value}`
      break
      
    case EffectType.BUFF_CHARISMA:
      player.special.C = Math.min(10, player.special.C + effect.value)
      valueChanged = effect.value
      success = true
      message = `Charisma increased by ${effect.value}`
      break
      
    case EffectType.BUFF_INTELLIGENCE:
      player.special.I = Math.min(10, player.special.I + effect.value)
      valueChanged = effect.value
      success = true
      message = `Intelligence increased by ${effect.value}`
      break
      
    case EffectType.BUFF_AGILITY:
      player.special.A = Math.min(10, player.special.A + effect.value)
      valueChanged = effect.value
      success = true
      message = `Agility increased by ${effect.value}`
      break
      
    case EffectType.BUFF_LUCK:
      player.special.L = Math.min(10, player.special.L + effect.value)
      valueChanged = effect.value
      success = true
      message = `Luck increased by ${effect.value}`
      break
      
    case EffectType.DEBUFF_STRENGTH:
      player.special.S = Math.max(1, player.special.S - effect.value)
      valueChanged = -effect.value
      success = true
      message = `Strength decreased by ${effect.value}`
      break
      
    case EffectType.DEBUFF_PERCEPTION:
      player.special.P = Math.max(1, player.special.P - effect.value)
      valueChanged = -effect.value
      success = true
      message = `Perception decreased by ${effect.value}`
      break
      
    case EffectType.DEBUFF_ENDURANCE:
      player.special.E = Math.max(1, player.special.E - effect.value)
      valueChanged = -effect.value
      success = true
      message = `Endurance decreased by ${effect.value}`
      break
      
    case EffectType.DEBUFF_CHARISMA:
      player.special.C = Math.max(1, player.special.C - effect.value)
      valueChanged = -effect.value
      success = true
      message = `Charisma decreased by ${effect.value}`
      break
      
    case EffectType.DEBUFF_INTELLIGENCE:
      player.special.I = Math.max(1, player.special.I - effect.value)
      valueChanged = -effect.value
      success = true
      message = `Intelligence decreased by ${effect.value}`
      break
      
    case EffectType.DEBUFF_AGILITY:
      player.special.A = Math.max(1, player.special.A - effect.value)
      valueChanged = -effect.value
      success = true
      message = `Agility decreased by ${effect.value}`
      break
      
    case EffectType.DEBUFF_LUCK:
      player.special.L = Math.max(1, player.special.L - effect.value)
      valueChanged = -effect.value
      success = true
      message = `Luck decreased by ${effect.value}`
      break
      
    case EffectType.STUN:
      // Stun effect is handled by the game engine, not here
      success = true
      message = 'Stunned!'
      break
      
    case EffectType.BLIND:
      // Blind effect is handled by the game engine, not here
      success = true
      message = 'Blinded!'
      break
      
    case EffectType.PARALYSIS:
      // Paralysis effect is handled by the game engine, not here
      success = true
      message = 'Paralyzed!'
      break
      
    default:
      message = `Unknown effect type: ${effect.type}`
      break
  }
  
  // Decrease effect duration
  effect.duration--
  
  // Remove effect if duration is 0 or less
  if (effect.duration <= 0) {
    const effectIndex = player.effects.findIndex(e => e.id === effect.id)
    if (effectIndex !== -1) {
      player.effects.splice(effectIndex, 1)
      effectRemoved = true
    }
  }
  
  return {
    success,
    message,
    valueChanged,
    effectRemoved
  }
}

/**
 * Apply all active effects to a player
 */
export function applyAllEffects(player: Player): EffectApplication[] {
  const results: EffectApplication[] = []
  
  // Create a copy of effects array to avoid modification during iteration
  const effectsCopy = [...player.effects]
  
  for (const effect of effectsCopy) {
    const result = applyEffect(player, effect)
    results.push(result)
  }
  
  return results
}

/**
 * Add a new effect to a player
 */
export function addEffect(player: Player, effect: Effect): boolean {
  // Check if effect already exists (for non-stackable effects)
  const existingEffect = player.effects.find(e => e.type === effect.type)
  
  if (existingEffect) {
    // For most effects, refresh duration instead of stacking
    if (effect.duration > existingEffect.duration) {
      existingEffect.duration = effect.duration
      existingEffect.value = Math.max(existingEffect.value, effect.value)
    }
    return true
  }
  
  // Add new effect
  player.effects.push(effect)
  return true
}

/**
 * Remove an effect from a player by ID
 */
export function removeEffect(player: Player, effectId: string): boolean {
  const effectIndex = player.effects.findIndex(e => e.id === effectId)
  
  if (effectIndex !== -1) {
    player.effects.splice(effectIndex, 1)
    return true
  }
  
  return false
}

/**
 * Remove all effects of a specific type from a player
 */
export function removeEffectsByType(player: Player, effectType: EffectType): number {
  const initialLength = player.effects.length
  player.effects = player.effects.filter(e => e.type !== effectType)
  return initialLength - player.effects.length
}

/**
 * Get all active effects of a specific type
 */
export function getEffectsByType(player: Player, effectType: EffectType): Effect[] {
  return player.effects.filter(e => e.type === effectType)
}

/**
 * Check if player has a specific effect type
 */
export function hasEffect(player: Player, effectType: EffectType): boolean {
  return player.effects.some(e => e.type === effectType)
}

/**
 * Get total modifier from all effects of a specific SPECIAL stat
 */
export function getStatModifier(player: Player, stat: keyof Special): number {
  let modifier = 0
  
  for (const effect of player.effects) {
    switch (effect.type) {
      case EffectType.BUFF_STRENGTH:
        if (stat === 'S') modifier += effect.value
        break
      case EffectType.BUFF_PERCEPTION:
        if (stat === 'P') modifier += effect.value
        break
      case EffectType.BUFF_ENDURANCE:
        if (stat === 'E') modifier += effect.value
        break
      case EffectType.BUFF_CHARISMA:
        if (stat === 'C') modifier += effect.value
        break
      case EffectType.BUFF_INTELLIGENCE:
        if (stat === 'I') modifier += effect.value
        break
      case EffectType.BUFF_AGILITY:
        if (stat === 'A') modifier += effect.value
        break
      case EffectType.BUFF_LUCK:
        if (stat === 'L') modifier += effect.value
        break
      case EffectType.DEBUFF_STRENGTH:
        if (stat === 'S') modifier -= effect.value
        break
      case EffectType.DEBUFF_PERCEPTION:
        if (stat === 'P') modifier -= effect.value
        break
      case EffectType.DEBUFF_ENDURANCE:
        if (stat === 'E') modifier -= effect.value
        break
      case EffectType.DEBUFF_CHARISMA:
        if (stat === 'C') modifier -= effect.value
        break
      case EffectType.DEBUFF_INTELLIGENCE:
        if (stat === 'I') modifier -= effect.value
        break
      case EffectType.DEBUFF_AGILITY:
        if (stat === 'A') modifier -= effect.value
        break
      case EffectType.DEBUFF_LUCK:
        if (stat === 'L') modifier -= effect.value
        break
    }
  }
  
  return modifier
}
