import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuthStore } from '../store/authStore'

const GamePage = () => {
  const { isAuthenticated } = useAuthStore()
  const navigate = useNavigate()

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login')
    }
  }, [isAuthenticated, navigate])

  if (!isAuthenticated) {
    return null
  }

  return (
    <div className="container  mx-auto px-4 py-8">
      <div className="bg-card p-6 h-screen  rounded-lg border" style={{ height: 'calc(100vh - 140px)' }}>
        <h1 className="text-2xl font-bold mb-4">Game Window</h1>
        <p className="text-muted-foreground">
          Main game area - all future game interactions will happen here
        </p>
      </div>
    </div>
  )
}

export default GamePage
