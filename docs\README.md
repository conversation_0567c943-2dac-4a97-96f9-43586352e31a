# Documentation - NuclearStory

Project documentation, database schemas, and API specifications.

## Contents

### Database Schemas
- `database/` - Database schema definitions
  - `auth-schema.sql` - Authentication service schema
  - `saves-schema.sql` - Save service schema
  - `erd.md` - Entity Relationship Diagrams

### API Documentation
- `api/` - API specifications and examples
  - `auth-api.md` - Authentication endpoints
  - `game-api.md` - Game engine endpoints
  - `story-api.md` - Story service endpoints
  - `save-api.md` - Save service endpoints

### Game Design
- `game-design/` - Game mechanics and design documents
  - `survival-mechanics.md` - Survival system design
  - `quest-system.md` - Quest generation system
  - `story-flow.md` - Narrative structure

### Architecture
- `architecture/` - System architecture documentation
  - `microservices.md` - Service communication patterns
  - `data-flow.md` - Data flow diagrams
  - `deployment.md` - Deployment strategies

## Quick Reference

### Database Connections
- Auth DB: `postgresql://user:pass@localhost:5432/auth_db`
- Saves DB: `postgresql://user:pass@localhost:5433/saves_db`

### API Endpoints
- Auth: `http://localhost:3001/api`
- Game Engine: `http://localhost:3002/api`
- Story: `http://localhost:3003/api`
- Saves: `http://localhost:3004/api`

### Swagger Documentation
- Auth: `http://localhost:3001/api/docs`
- Game Engine: `http://localhost:3002/api/docs`
- Story: `http://localhost:3003/api/docs`
- Saves: `http://localhost:3004/api/docs`
