import { ItemType, ItemRarity } from '../enums';

export interface WeaponDamage {
  min: number;
  max: number;
  type: 'normal' | 'laser' | 'plasma' | 'explosive' | 'fire' | 'electric' | 'poison';
}

export interface WeaponStats {
  damage: WeaponDamage;
  accuracy: number; // 0-100
  criticalChance: number; // 0-100
  criticalMultiplier: number;
  range: number; // in grid cells
  actionPointCost: number;

  // Ammo and reloading
  usesAmmo: boolean;
  ammoType?: string;
  magazineSize?: number;
  currentAmmo?: number;
  reloadAPCost?: number; // AP cost to reload

  // Durability
  durability: number;
  maxDurability: number;
  degradationRate: number;

  // Special properties
  armorPenetration: number;
  burstFire: boolean;
  burstSize?: number;
  fullAuto: boolean;

  // Requirements
  SParamRequired: number;
  PParamRequired: number;
  EParamRequired: number;
  IParamRequired: number;
  AParamRequired: number;
}

export interface WeaponModification {
  id: string;
  name: string;
  description: string;
  slot: 'barrel' | 'scope' | 'stock' | 'magazine' | 'trigger' | 'grip';
  effects: {
    damageBonus?: number;
    accuracyBonus?: number;
    rangeBonus?: number;
    criticalBonus?: number;
    apCostReduction?: number;
    durabilityBonus?: number;
  };
  requirements?: {
    skill?: Record<string, number>;
    items?: string[]; // Component item IDs
  };
  isInstalled: boolean;
}

// export interface WeaponAttackMode {
//   id: string;
//   name: string;
//   description: string;
//   apCost: number;
//   accuracyModifier: number;
//   damageModifier: number;
//   rangeModifier: number;
//   ammoConsumption: number;
//   specialEffects?: string[];
// }

export interface Weapon {
  id: string;
  name: string;
  description: string;
  type: ItemType;
  rarity: ItemRarity;

  // Basic properties
  weight: number;
  value: number;

  // Weapon classification
  weaponRange: 'melee' | 'ranged' | 'mixed'; // для универсального оружия
  weaponType: 'melee' | 'light ballistic' | 'heavy ballistic' | 'energy' | 'explosive' | 'thrown' | 'exotic';
  weaponClass: 'unarmed' | 'blade' | 'club'  | 'pistol' | 'rifle' | 'shotgun' | 'smg' | 'lmg' | 'sniper' | 'launcher'; 
  // Combat stats
  stats: WeaponStats;

  // Attack modes
  attackModel: 'single' | 'burst' | 'full-auto';
  
  // Modifications
  modificationSlots: string[]; // Available mod slots
  installedMods: WeaponModification[];
  maxMods: number;

  // Appearance and sound
  sprite?: string;
  icon?: string;
  soundEffects?: {
    fire?: string;
    reload?: string;
    empty?: string;
  };

  // Special abilities
  specialAbilities: string[]; // Ability IDs

  // Crafting and repair
  canBeCrafted: boolean;
  craftingRecipe?: {
    components: Record<string, number>; // Item ID -> quantity
    skill: Record<string, number>; // Skill -> level required
    tools?: string[]; // Tool item IDs
  };
  canBeRepaired: boolean;
  repairCost?: Record<string, number>; // Item ID -> quantity

  // Lore and flavor
  manufacturer?: string;
  yearMade?: number;
  loreText?: string;

  // Flags
  isUnique: boolean;
  isQuestItem: boolean;
  canBeSold: boolean;
  canBeDropped: boolean;
  canBeStolen: boolean;

  // Timestamps
  createdAt: Date;
  lastUsedAt?: Date;
  lastRepairedAt?: Date;
}
